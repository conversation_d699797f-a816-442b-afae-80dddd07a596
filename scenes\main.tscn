[gd_scene load_steps=33 format=3 uid="uid://c5nb3v5dt7aij"]

[ext_resource type="Texture2D" uid="uid://bp78rx4flkmc8" path="res://assets/background.png" id="1_0wfyh"]
[ext_resource type="Texture2D" uid="uid://b7q67cvagi3w7" path="res://assets/player.png" id="2_sugp2"]

[sub_resource type="AtlasTexture" id="AtlasTexture_jyhfs"]
atlas = ExtResource("2_sugp2")
region = Rect2(0, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_tbgi4"]
atlas = ExtResource("2_sugp2")
region = Rect2(64, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_tefeu"]
atlas = ExtResource("2_sugp2")
region = Rect2(128, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_o6xl0"]
atlas = ExtResource("2_sugp2")
region = Rect2(192, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_tipki"]
atlas = ExtResource("2_sugp2")
region = Rect2(256, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_85g3d"]
atlas = ExtResource("2_sugp2")
region = Rect2(320, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_choun"]
atlas = ExtResource("2_sugp2")
region = Rect2(384, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_ya4ey"]
atlas = ExtResource("2_sugp2")
region = Rect2(448, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_eb6dy"]
atlas = ExtResource("2_sugp2")
region = Rect2(512, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_trceg"]
atlas = ExtResource("2_sugp2")
region = Rect2(576, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_a8y0u"]
atlas = ExtResource("2_sugp2")
region = Rect2(640, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_jkv2x"]
atlas = ExtResource("2_sugp2")
region = Rect2(704, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_jbj1t"]
atlas = ExtResource("2_sugp2")
region = Rect2(768, 0, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_muem4"]
atlas = ExtResource("2_sugp2")
region = Rect2(0, 192, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_dp3eg"]
atlas = ExtResource("2_sugp2")
region = Rect2(64, 192, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_0ld40"]
atlas = ExtResource("2_sugp2")
region = Rect2(128, 192, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_gqmmt"]
atlas = ExtResource("2_sugp2")
region = Rect2(192, 192, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_yc10j"]
atlas = ExtResource("2_sugp2")
region = Rect2(0, 128, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_jscy8"]
atlas = ExtResource("2_sugp2")
region = Rect2(64, 128, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_pm3ni"]
atlas = ExtResource("2_sugp2")
region = Rect2(128, 128, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_y6deb"]
atlas = ExtResource("2_sugp2")
region = Rect2(192, 128, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_og1vs"]
atlas = ExtResource("2_sugp2")
region = Rect2(256, 128, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_2wyq8"]
atlas = ExtResource("2_sugp2")
region = Rect2(320, 128, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_vxglm"]
atlas = ExtResource("2_sugp2")
region = Rect2(384, 128, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_2f3dj"]
atlas = ExtResource("2_sugp2")
region = Rect2(448, 128, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_yq6so"]
atlas = ExtResource("2_sugp2")
region = Rect2(512, 128, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_fv21b"]
atlas = ExtResource("2_sugp2")
region = Rect2(576, 128, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_tel4y"]
atlas = ExtResource("2_sugp2")
region = Rect2(640, 128, 64, 64)

[sub_resource type="AtlasTexture" id="AtlasTexture_qkpxi"]
atlas = ExtResource("2_sugp2")
region = Rect2(704, 128, 64, 64)

[sub_resource type="SpriteFrames" id="SpriteFrames_5q0nq"]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_jyhfs")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_tbgi4")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_tefeu")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_o6xl0")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_tipki")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_85g3d")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_choun")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ya4ey")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_eb6dy")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_trceg")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_a8y0u")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_jkv2x")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_jbj1t")
}],
"loop": true,
"name": &"attack",
"speed": 10.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_muem4")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_dp3eg")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_0ld40")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_gqmmt")
}],
"loop": true,
"name": &"idle",
"speed": 4.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_yc10j")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_jscy8")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_pm3ni")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_y6deb")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_og1vs")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_2wyq8")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_vxglm")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_2f3dj")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_yq6so")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_fv21b")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_tel4y")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_qkpxi")
}],
"loop": true,
"name": &"walk",
"speed": 12.0
}]

[node name="root" type="Node2D"]

[node name="Background" type="Sprite2D" parent="."]
position = Vector2(576, 323.5)
scale = Vector2(1.85806, 1.80278)
texture = ExtResource("1_0wfyh")

[node name="Player" type="AnimatedSprite2D" parent="."]
position = Vector2(576, 531)
scale = Vector2(3.125, 3.125)
sprite_frames = SubResource("SpriteFrames_5q0nq")
animation = &"walk"
frame_progress = 0.0201456
