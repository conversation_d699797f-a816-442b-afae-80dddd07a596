# Godot-specific ignores
.godot/
.import/

# Imported translations (automatically generated from CSV files)
*.translation

# Mono-specific ignores (C# projects)
.mono/
data_*/
mono_crash.*.json

# System/tool-specific ignores
.tmp/
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE and editor ignores
.vscode/
.idea/
*.swp
*.swo
*~

# Build and export ignores
/build/
/builds/
/export/
/exports/
*.tmp
*.exe
*.pck
*.zip
*.dmg
*.app
*.apk
*.aab
*.ipa

# Platform-specific build outputs
/android/
/ios/
/windows/
/linux/
/macos/
/web/

# Export templates and presets (keep export_presets.cfg but ignore sensitive data)
# Uncomment the following line if you want to ignore export presets entirely:
# export_presets.cfg

# Logs and crash reports
logs/
*.log
crash_handler_*.txt
godot_crash_*.txt

# Addons that should be downloaded/managed separately
# Uncomment specific addons you don't want to track:
# addons/*/

# User-specific project settings
.godot/editor/
.godot/imported/
.godot/uid_cache.tmp
.godot/global_script_class_cache.cfg

# Backup files
*.bak
*.backup
*~

# Temporary files
*.tmp
*.temp